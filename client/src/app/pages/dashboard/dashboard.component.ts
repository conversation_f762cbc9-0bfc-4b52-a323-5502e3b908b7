import { Component, OnInit } from '@angular/core';
import { TableModule } from 'primeng/table';
import { Tag } from 'primeng/tag';
import { ConfirmDialog } from 'primeng/confirmdialog';
import { Toast } from 'primeng/toast';
import { Select } from 'primeng/select';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

import { Proposal } from '../../../lib/models';
import { ProposalService } from '../../services';
import { ConfirmationService, MessageService } from 'primeng/api';

@Component({
  selector: 'chm-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    Tag,
    ConfirmDialog,
    Toast,
    Select,
    TableModule,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css',
})
export class DashboardComponent implements OnInit {
  proposals: Proposal[] = [];
  loading = true;
  searchValue = '';
  selectedProposals: Proposal[] = [];

  statusOptions = [
    { label: 'All Statuses', value: null },
    { label: 'Draft', value: 'draft' },
    { label: 'Sent', value: 'sent' },
    { label: 'Accepted', value: 'accepted' },
    { label: 'Partially Paid', value: 'partially_paid' },
    { label: 'Paid', value: 'paid' },
  ];

  selectedStatus: any = null;

  constructor(
    private proposalService: ProposalService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private router: Router,
  ) {}

  ngOnInit() {
    this.loadProposals();
  }

  loadProposals() {
    this.loading = true;
    this.proposalService.getAll().subscribe({
      next: (proposals) => {
        this.proposals = proposals;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading proposals:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load proposals',
        });
        this.loading = false;
      },
    });
  }

  getStatusSeverity(
    status: string,
  ): 'success' | 'info' | 'warning' | 'danger' | 'secondary' {
    switch (status) {
      case 'paid':
        return 'success';
      case 'accepted':
      case 'partially_paid':
        return 'info';
      case 'sent':
        return 'warning';
      case 'draft':
        return 'secondary';
      default:
        return 'secondary';
    }
  }

  formatCurrency(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount);
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  viewProposal(proposal: Proposal) {
    // Navigate to proposal view
    this.router.navigate(['/p', proposal.id]);
  }

  editProposal(proposal: Proposal) {
    // Navigate to proposal editor with existing proposal
    this.router.navigate(['/proposal-editor'], {
      queryParams: { id: proposal.id },
    });
  }

  deleteProposal(proposal: Proposal) {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete proposal "${proposal.project_title}"?`,
      header: 'Delete Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.proposalService.delete(proposal.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Deleted',
                detail: 'Proposal deleted successfully',
              });
              this.loadProposals();
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete proposal',
              });
            }
          },
          error: (error) => {
            console.error('Error deleting proposal:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete proposal',
            });
          },
        });
      },
    });
  }

  sendProposal(proposal: Proposal) {
    // TODO: Implement send functionality
    this.messageService.add({
      severity: 'info',
      summary: 'Sent',
      detail: `Proposal "${proposal.project_title}" sent to client`,
    });
  }

  createNewProposal() {
    this.router.navigate(['/proposal-editor']);
  }

  clear(table: any) {
    table.clear();
    this.searchValue = '';
  }

  onGlobalFilter(table: any, event: Event) {
    table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
  }
}
